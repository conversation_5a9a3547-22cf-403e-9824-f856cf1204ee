import React from 'react';

interface WindowTitleBarProps {
  title: string;
  isActive?: boolean;
  onClose?: () => void;
  onMinimize?: () => void;
  onMaximize?: () => void;
  isMainWindow?: boolean;
  isFullscreen?: boolean;
}

const WindowTitleBar: React.FC<WindowTitleBarProps> = ({
  title = "ResCueX",
  isActive = true,
  onClose,
  onMinimize,
  onMaximize,
  isMainWindow = false,
  isFullscreen = false
}) => {
  const handleMaximizeClick = () => {
    // Attempt to enter fullscreen
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
      });
    } else {
      console.warn("Fullscreen API is not supported by this browser.");
    }
  };

  if (isMainWindow) {
    return (
      <div className="flex items-center justify-between h-8 px-2 bg-[#0078d7] border-b border-[#005a9c]">
        <div className="flex items-center">
          <img src="/rescuex-icon.png" alt="ResCueX" className="h-4 w-4 mr-2" onError={e => {
            e.currentTarget.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'/%3E%3Cpolyline points='9 22 9 12 15 12 15 22'/%3E%3C/svg%3E";
          }} />
          <span className="text-white text-sm font-normal">
            {title}
          </span>
        </div>
        <div className="flex items-center">
          <button 
            className="w-[46px] h-8 flex items-center justify-center hover:bg-white/10 active:bg-white/20 transition-colors" 
            onClick={onMinimize}
            aria-label="Minimize"
          >
            <svg width="10" height="10" viewBox="0 0 10.2 1" fill="white">
              <rect x="0" y="0" width="10.2" height="1"></rect>
            </svg>
          </button>
          <button 
            className="w-[46px] h-8 flex items-center justify-center hover:bg-white/10 active:bg-white/20 transition-colors" 
            onClick={handleMaximizeClick}
            aria-label="Maximize"
          >
            <svg width="10" height="10" viewBox="0 0 10 10" fill="white">
              <path d="M0,0v10h10V0H0z M9,9H1V1h8V9z"></path>
            </svg>
          </button>
          <button 
            className="w-[46px] h-8 flex items-center justify-center hover:bg-red-600 active:bg-red-400 transition-colors" 
            onClick={onClose}
            aria-label="Close"
          >
            <svg width="10" height="10" viewBox="0 0 10.2 10.2" fill="white">
              <polygon points="10.2,0.7 9.5,0 5.1,4.4 0.7,0 0,0.7 4.4,5.1 0,9.5 0.7,10.2 5.1,5.8 9.5,10.2 10.2,9.5 5.8,5.1"></polygon>
            </svg>
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`flex items-center justify-between h-6 px-2 ${isActive ? 'bg-[#333333]' : 'bg-[#E1E1E1]'} border-b border-[#aaaaaa]`}>
      <div className="flex items-center">
        <span className={`text-xs font-normal ${isActive ? 'text-white' : 'text-black'}`}>
          {title}
        </span>
      </div>
      <button 
        className={`w-6 h-6 flex items-center justify-center hover:bg-red-500 hover:text-white transition-colors rounded-sm`} 
        onClick={onClose}
        aria-label="Close"
      >
        <svg width="8" height="8" viewBox="0 0 10.2 10.2" fill={isActive ? "white" : "black"}>
          <polygon points="10.2,0.7 9.5,0 5.1,4.4 0.7,0 0,0.7 4.4,5.1 0,9.5 0.7,10.2 5.1,5.8 9.5,10.2 10.2,9.5 5.8,5.1"></polygon>
        </svg>
      </button>
    </div>
  );
};

export default WindowTitleBar;
