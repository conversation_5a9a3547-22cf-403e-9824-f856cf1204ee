
import React, { useState } from "react";
import { ChevronRight, ChevronDown, Search, ChevronLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarShortcut,
  MenubarTrigger,
  MenubarSub,
  MenubarSubContent,
  MenubarSubTrigger,
} from "@/components/ui/menubar";
import { Separator } from "@/components/ui/separator";
import WindowTitleBar from "./WindowTitleBar";
import { IncidentReportWindow } from "./IncidentReportWindow";
import { CallQueueWindow } from "./CallQueueWindow";
import { CallQueue894Window } from "./CallQueue894Window";
import { CallQueue1262Window } from "./CallQueue1262Window";
import { CallQueue69Window } from "./CallQueue69Window";
import { CreateReportWindow } from "./CreateReportWindow";
import { ViewReportsWindow } from "./ViewReportsWindow";
import { DispatchConsoleWindow } from "./DispatchConsoleWindow";
import { OperatorStatusWindow } from "./OperatorStatusWindow";
import { AlarmPanelWindow } from "./AlarmPanelWindow"; // Import Alarm Panel window

// Define props interface
interface WindowsDialogProps {
  isMinimized: boolean;
  setIsMinimized: React.Dispatch<React.SetStateAction<boolean>>;
}

export const WindowsDialog: React.FC<WindowsDialogProps> = ({ isMinimized, setIsMinimized }) => {
  const [isMaximized, setIsMaximized] = useState(true);
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    Environment: true,
  });
  const [selectedItem, setSelectedItem] = useState("Keyboard");
  const [selectedCommand, setSelectedCommand] = useState("View.SynchronizeClassView");
  const [searchTerm, setSearchTerm] = useState("");
  const [showIncidentReport, setShowIncidentReport] = useState(false);
  const [showCallQueue, setShowCallQueue] = useState(false); // State for Call Queue window
  const [showCallQueue894, setShowCallQueue894] = useState(false); // State for Call Queue #894 window
  const [showCallQueue1262, setShowCallQueue1262] = useState(false); // State for Call Queue #1262 window
  const [showCallQueue69, setShowCallQueue69] = useState(false); // State for Call Queue #69 window
  const [showCreateReport, setShowCreateReport] = useState(false);
  const [showViewReports, setShowViewReports] = useState(false);
  const [showDispatchConsole, setShowDispatchConsole] = useState(false);
  const [showOperatorStatus, setShowOperatorStatus] = useState(false);
  const [showAlarmPanel, setShowAlarmPanel] = useState(false); // State for Alarm Panel window

  const toggleExpand = (item: string) => {
    setExpandedItems({
      ...expandedItems,
      [item]: !expandedItems[item],
    });
  };

  const toggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleClose = () => {
    // Reuse minimize logic to "fake close" the main window
    setIsMinimized(true); // Use the setter prop
  };

  const closeIncidentReport = () => {
    setShowIncidentReport(false);
  };

  // Function to close the Call Queue window
  const closeCallQueue = () => {
    setShowCallQueue(false);
  };
  
  // Function to close the Call Queue #894 window
  const closeCallQueue894 = () => {
    setShowCallQueue894(false);
  };
  
  // Function to close the Call Queue #1262 window
  const closeCallQueue1262 = () => {
    setShowCallQueue1262(false);
  };
  
  // Function to close the Call Queue #69 window
  const closeCallQueue69 = () => {
    setShowCallQueue69(false);
  };

  // Function to close the Create Report window
  const closeCreateReport = () => {
    setShowCreateReport(false);
  };

  // Function to close the View Reports window
  const closeViewReports = () => {
    setShowViewReports(false);
  };

  // Function to close the Dispatch Console window
  const closeDispatchConsole = () => {
    setShowDispatchConsole(false);
  };

  // Function to close the Operator Status window
  const closeOperatorStatus = () => {
    setShowOperatorStatus(false);
  };

  // Function to close the Alarm Panel window
  const closeAlarmPanel = () => {
    setShowAlarmPanel(false);
  };

  if (isMinimized) return null; // Use the isMinimized prop

  return (
    <div className={cn("flex flex-col bg-[#f0f0f0] shadow-xl border border-[#aaaaaa]",
        isMaximized ? "fixed inset-0 bottom-12 z-30" : "w-[750px] h-[600px]")}>
      <WindowTitleBar
        title="ResCueX"
        isMainWindow={true}
        isFullscreen={isMaximized}
        onClose={handleClose}
        onMinimize={() => setIsMinimized(true)} // Use the setter prop directly
        onMaximize={toggleMaximize}
      />

      <Menubar className="rounded-none border-0 border-b border-[#aaaaaa] bg-[#f0f0f0] px-1 py-0 h-7">
        <MenubarMenu>
          <MenubarTrigger className="text-xs px-2 py-1 rounded-none focus:bg-[#e5e5e5] data-[state=open]:bg-[#e5e5e5]">Arkiv</MenubarTrigger>
          <MenubarContent className="bg-[#f0f0f0] border-[#aaaaaa] rounded-none shadow-md">
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Ny <MenubarShortcut>Ctrl+N</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Öppna... <MenubarShortcut>Ctrl+O</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Spara <MenubarShortcut>Ctrl+S</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Spara som... <MenubarShortcut>Ctrl+Shift+S</MenubarShortcut>
            </MenubarItem>
            <MenubarSeparator />
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Import...
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Export...
            </MenubarItem>
            <MenubarSeparator />
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Stäng <MenubarShortcut>Alt+F4</MenubarShortcut>
            </MenubarItem>
          </MenubarContent>
        </MenubarMenu>

        <MenubarMenu>
          <MenubarTrigger className="text-xs px-2 py-1 rounded-none focus:bg-[#e5e5e5] data-[state=open]:bg-[#e5e5e5]">Redigera</MenubarTrigger>
          <MenubarContent className="bg-[#f0f0f0] border-[#aaaaaa] rounded-none shadow-md">
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Ångra <MenubarShortcut>Ctrl+Z</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Upprepa <MenubarShortcut>Ctrl+Y</MenubarShortcut>
            </MenubarItem>
            <MenubarSeparator />
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Kasta <MenubarShortcut>Ctrl+X</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Kopiera <MenubarShortcut>Ctrl+C</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Klistra in <MenubarShortcut>Ctrl+V</MenubarShortcut>
            </MenubarItem>
            <MenubarSeparator />
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Sök... <MenubarShortcut>Ctrl+F</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Ersätt... <MenubarShortcut>Ctrl+H</MenubarShortcut>
            </MenubarItem>
          </MenubarContent>
        </MenubarMenu>

        <MenubarMenu>
          <MenubarTrigger className="text-xs px-2 py-1 rounded-none focus:bg-[#e5e5e5] data-[state=open]:bg-[#e5e5e5]">Visa</MenubarTrigger>
          <MenubarContent className="bg-[#f0f0f0] border-[#aaaaaa] rounded-none shadow-md">
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Zooma in <MenubarShortcut>Ctrl++</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Zooma ut <MenubarShortcut>Ctrl+-</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Återställ zoom <MenubarShortcut>Ctrl+0</MenubarShortcut>
            </MenubarItem>
            <MenubarSeparator />
            <MenubarSub>
              <MenubarSubTrigger className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
                Verktygsfält
              </MenubarSubTrigger>
              <MenubarSubContent className="bg-[#f0f0f0] border-[#aaaaaa] rounded-none shadow-md">
                <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
                  Standard
                </MenubarItem>
                <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
                  Formatering
                </MenubarItem>
                <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
                  Navigering
                </MenubarItem>
              </MenubarSubContent>
            </MenubarSub>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Statusfält
            </MenubarItem>
          </MenubarContent>
        </MenubarMenu>

        <MenubarMenu>
          <MenubarTrigger className="text-xs px-2 py-1 rounded-none focus:bg-[#e5e5e5] data-[state=open]:bg-[#e5e5e5]">Verktyg</MenubarTrigger>
          <MenubarContent className="bg-[#f0f0f0] border-[#aaaaaa] rounded-none shadow-md">
            <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowIncidentReport(true)}
            >
              Incidentrapportering
            </MenubarItem>
            <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowCallQueue(true)} // Toggle Call Queue window
            >
              Samtalskö
            </MenubarItem>
            <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowCallQueue894(true)} // Toggle Call Queue #894 window
            >
              Samtalskö #894
            </MenubarItem>
            <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowCallQueue1262(true)} // Toggle Call Queue #1262 window
            >
              Samtalskö #1262
            </MenubarItem>
            <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowCallQueue69(true)} // Toggle Call Queue #69 window
            >
              Samtalskö #69
            </MenubarItem>
             <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowDispatchConsole(true)} // Toggle Dispatch Console window
            >
              Utryckningskonsol
            </MenubarItem>
             <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowOperatorStatus(true)} // Toggle Operator Status window
            >
              Operatörsstatus
            </MenubarItem>
            <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowAlarmPanel(true)} // Toggle Alarm Panel window
            >
              Larmpanel
            </MenubarItem>
            <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              // Added width/height features to suggest opening in a new window
              onClick={() => window.open('https://startling-longma-62c677.netlify.app/', '_blank', 'width=1000,height=700,noopener,noreferrer')}
            >
              Kartvy (Extern)
            </MenubarItem>
            <MenubarSeparator />
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Alternativ...
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Anpassa...
            </MenubarItem>
          </MenubarContent>
        </MenubarMenu>

        <MenubarMenu>
          <MenubarTrigger className="text-xs px-2 py-1 rounded-none focus:bg-[#e5e5e5] data-[state=open]:bg-[#e5e5e5]">Rapporter</MenubarTrigger>
          <MenubarContent className="bg-[#f0f0f0] border-[#aaaaaa] rounded-none shadow-md">
            <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowCreateReport(true)}
            >
              Skapa rapport
            </MenubarItem>
            <MenubarItem
              className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white"
              onClick={() => setShowViewReports(true)}
            >
              Visa rapporter
            </MenubarItem>
          </MenubarContent>
        </MenubarMenu>

        <MenubarMenu>
          <MenubarTrigger className="text-xs px-2 py-1 rounded-none focus:bg-[#e5e5e5] data-[state=open]:bg-[#e5e5e5]">Hjälp</MenubarTrigger>
          <MenubarContent className="bg-[#f0f0f0] border-[#aaaaaa] rounded-none shadow-md">
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Visa hjälp <MenubarShortcut>F1</MenubarShortcut>
            </MenubarItem>
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Användarhandledning
            </MenubarItem>
            <MenubarSeparator />
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Kontrollera för uppdateringar...
            </MenubarItem>
            <MenubarSeparator />
            <MenubarItem className="text-xs px-2 py-1 focus:bg-[#0078d7] focus:text-white">
              Om
            </MenubarItem>
          </MenubarContent>
        </MenubarMenu>
      </Menubar>

      {/* Main content area - allows floating windows */}
      <div className="flex-1 bg-[#b9b9b9] flex flex-col relative overflow-hidden"> {/* Added overflow-hidden */}
        {/* Render Incident Report Window if shown */}
        {showIncidentReport && (
          <IncidentReportWindow onClose={closeIncidentReport} />
        )}

        {/* Render Call Queue Window if shown */}
        {showCallQueue && (
          <CallQueueWindow onClose={closeCallQueue} />
        )}

        {/* Render Call Queue #894 Window if shown */}
        {showCallQueue894 && (
          <CallQueue894Window onClose={closeCallQueue894} />
        )}

        {/* Render Call Queue #1262 Window if shown */}
        {showCallQueue1262 && (
          <CallQueue1262Window onClose={closeCallQueue1262} />
        )}

        {/* Render Call Queue #69 Window if shown */}
        {showCallQueue69 && (
          <CallQueue69Window onClose={closeCallQueue69} />
        )}

        {/* Render Create Report Window if shown */}
        {showCreateReport && (
          <CreateReportWindow onClose={closeCreateReport} />
        )}

        {/* Render View Reports Window if shown */}
        {showViewReports && (
          <ViewReportsWindow onClose={closeViewReports} />
        )}

        {/* Render Dispatch Console Window if shown */}
        {showDispatchConsole && (
          <DispatchConsoleWindow onClose={closeDispatchConsole} />
        )}

        {/* Render Operator Status Window if shown */}
        {showOperatorStatus && (
          <OperatorStatusWindow onClose={closeOperatorStatus} />
        )}

        {/* Render Alarm Panel Window if shown */}
        {showAlarmPanel && (
          <AlarmPanelWindow onClose={closeAlarmPanel} />
        )}

        {/* Placeholder div if no windows are open, or just let the background show */}
        {/* <div className="flex-1"></div> */}
      </div>

      {/* Status bar - moved to bottom of application */}
      <div className="h-6 bg-[#dfdfdf] border-t border-[#aaaaaa] flex items-center px-2 justify-between">
        <div className="flex items-center space-x-2 text-xs text-[#606060]">
          <span>ResCueX v1.0.2</span>
          <span>|</span>
          <span>Anslutning: Säker</span>
          <span>|</span>
          <span>Databas: Ansluten</span>
          <span>|</span>
          <span>CAD-system: Online</span>
          <span>|</span>
          <span>Aktiva händelser: 3</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span className="text-xs text-[#606060]">System i drift</span>
        </div>
      </div>
    </div>
  );
};
